#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for LoginPage implementation

This script tests the new LoginPage class to ensure it works correctly
with the existing configuration and UI automation framework.

@Date       :   2024-02-19
<AUTHOR>   KunoLu
@Email      :   <EMAIL>
@File       :   test_login_page.py
@Software   :   PyCharm
"""

import sys
import os
import toml

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

try:
    from neox_test_scenarios.yakushi.pages.login.login_page import LoginPage
    from neox_test_common import logger
    
    def load_test_config():
        """Load test configuration from yakushi.toml"""
        config_path = os.path.join(
            project_root, 
            "Automation", 
            "Windows", 
            "KENTA", 
            "conf", 
            "yakushi.toml"
        )
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return toml.load(f)
        else:
            logger.error(f"Config file not found: {config_path}")
            return None
    
    def test_login_page_initialization():
        """Test LoginPage initialization"""
        logger.info("Testing LoginPage initialization...")
        
        config = load_test_config()
        if not config:
            logger.error("Failed to load configuration")
            return False
        
        try:
            login_page = LoginPage(config)
            logger.info("LoginPage initialized successfully")
            
            # Test configuration access
            username = login_page.get_element_config("yakushi.modules.login.account")
            password = login_page.get_element_config("yakushi.modules.login.password")
            
            if username and password:
                logger.info(f"Configuration loaded successfully - Username: {username}")
                return True
            else:
                logger.error("Failed to load login credentials from config")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing LoginPage: {e}")
            return False
    
    def test_element_config_access():
        """Test element configuration access"""
        logger.info("Testing element configuration access...")
        
        config = load_test_config()
        if not config:
            return False
        
        try:
            login_page = LoginPage(config)
            
            # Test various configuration paths
            test_paths = [
                "yakushi.modules.login.control.box.user",
                "yakushi.modules.login.control.box.password", 
                "yakushi.modules.login.control.btn.login",
                "yakushi.modules.common.window.main.auto_id",
                "yakushi.modules.common.window.float.auto_id"
            ]
            
            for path in test_paths:
                config_value = login_page.get_element_config(path)
                if config_value:
                    logger.info(f"✓ Config path '{path}': {config_value}")
                else:
                    logger.warning(f"✗ Config path '{path}': Not found")
            
            return True
            
        except Exception as e:
            logger.error(f"Error testing element config access: {e}")
            return False
    
    def main():
        """Main test function"""
        logger.info("Starting LoginPage tests...")
        
        tests = [
            ("LoginPage Initialization", test_login_page_initialization),
            ("Element Config Access", test_element_config_access),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n--- Running: {test_name} ---")
            try:
                if test_func():
                    logger.info(f"✓ PASSED: {test_name}")
                    passed += 1
                else:
                    logger.error(f"✗ FAILED: {test_name}")
            except Exception as e:
                logger.error(f"✗ ERROR in {test_name}: {e}")
        
        logger.info(f"\n=== Test Results ===")
        logger.info(f"Passed: {passed}/{total}")
        logger.info(f"Failed: {total - passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed!")
            return True
        else:
            logger.error("❌ Some tests failed!")
            return False

    if __name__ == "__main__":
        success = main()
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"Import error: {e}")
    print("This is expected during development. The LoginPage class will be available once the module structure is complete.")
    sys.exit(0)
